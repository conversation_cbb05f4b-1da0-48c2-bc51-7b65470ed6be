using UnityEngine;
using Fusion;
using SimpleFPS;
using System.Collections.Generic;
using DG.Tweening;

namespace SimpleFPS {
    public class AltPryatkiGameMode : NetworkBehaviour, IGameModeConfig {
        public static AltPryatkiGameMode Instance { get; private set; }

        #region Fields
        [Header("Gameplay Settings")]
        [SerializeField] private float lobbyDuration = 30f;
        [SerializeField] private float matchDuration = 120f;
        [SerializeField] private float playerRespawnTime = 5f;
        [SerializeField] private GameManager gameManager;

        [Header("Mode Description")]
        [TextArea][SerializeField] private string modeDescription = "Alternative Pryatki: Leader doesn't lose when time runs out. Get money for each kill. Pick up LaserItem to become leader.";

        [Header("References")]
        [SerializeField] private Transform loserSpawnPoint;
        public NetworkPrefabRef laserItem;
        [SerializeField] private GameObject blindfoldPrefab; // Blindfold visual for leader

        [<PERSON><PERSON>("Money Settings")]
        [SerializeField] private int moneyPerKill = 100;

        [Head<PERSON>("Blindness Settings")]
        [SerializeField] private float blindnessDuration = 3f;
        #endregion

        #region Networked Properties
        [Networked] public PlayerRef CurrentLeader { get; private set; }
        #endregion

        #region Private Variables
        // Store blindfold instances for each player
        private Dictionary<PlayerRef, GameObject> playerBlindfolds = new Dictionary<PlayerRef, GameObject>();
        #endregion

        #region Properties
        public float LobbyDuration => lobbyDuration;
        public float MatchDuration => matchDuration;
        public float PlayerRespawnTime => playerRespawnTime;
        public Transform LoserSpawnPoint => loserSpawnPoint;
        public string ModeDescription => modeDescription;
        #endregion

        #region Unity Lifecycle
        private void Awake() {
            if (Instance == null) {
                Instance = this;
            }
            else if (Instance != this) {
                Destroy(gameObject);
            }
            if (gameManager == null) {
                gameManager = GameManager.Instance;
            }
        }

        public override void Spawned() {
            // We only allow the server (StateAuthority in Fusion) to execute game logic.
            if (!Object.HasStateAuthority) {
                enabled = false;
                return;
            }

            // Make sure we have a reference to this instance.
            if (Instance == null) {
                Instance = this;
            }
        }
        #endregion

        #region Round Logic
        public void OnRoundStarted() {
            if (!Object.HasStateAuthority) {
                return;
            }

            ChooseRandomLeader();
        }

        public void OnRoundEnded() {
            if (!Object.HasStateAuthority) {
                return;
            }

            DetermineWinnersAndLosers();
        }
        #endregion

        #region Winners & Losers Determination
        public void DetermineWinnersAndLosers() {
            // In AltPryatki mode, leader doesn't lose when time runs out
            // Game ends naturally when only one player remains or everyone dies
            // No special logic needed here - GameManager handles the rest
        }
        #endregion

        #region Reset Mode State
        public void ResetModeState() {
            if (!Object.HasStateAuthority) { return; }

            // Remove all blindfolds
            RemoveAllBlindfolds();

            // Remove the current leader.
            if (CurrentLeader != PlayerRef.None) {
                SetLeaderInitially(PlayerRef.None);
            }

            if (Runner != null && gameManager != null && gameManager.PlayerData.Count > 0) {
                foreach (var kvp in gameManager.PlayerData) {
                    PlayerRef pRef = kvp.Key;
                    if (Runner.TryGetPlayerObject(pRef, out NetworkObject playerObj)) {
                        var pc = playerObj.GetComponent<PlayerController>();
                        if (pc != null && pc.CurrentItem != null) {
                            Runner.Despawn(pc.CurrentItem);
                            pc.CurrentItem = null;
                        }
                    }
                }
            }
        }
        #endregion

        #region Specific Game Mode Rules
        private void ChooseRandomLeader() {
            if (!Object.HasStateAuthority) {
                return;
            }

            List<PlayerRef> alivePlayers = new List<PlayerRef>();

            foreach (var kv in gameManager.PlayerData) {
                if (kv.Value.IsAlive && kv.Value.IsConnected) {
                    alivePlayers.Add(kv.Key);
                }
            }

            if (alivePlayers.Count > 0) {
                int randomIndex = Random.Range(0, alivePlayers.Count);
                SetLeaderInitially(alivePlayers[randomIndex]);
            }
        }

        public void SetLeaderInitially(PlayerRef newLeader) {
            if (!Object.HasStateAuthority) {
                return;
            }

            // 1) Remove previous leader
            if (CurrentLeader != PlayerRef.None && CurrentLeader != newLeader) {
                if (Runner.TryGetPlayerObject(CurrentLeader, out var oldLeaderObj)) {
                    var oldLeaderPC = oldLeaderObj.GetComponent<PlayerController>();
                    oldLeaderPC?.RPC_SetLeader(false);

                    // Remove LaserItem from old leader
                    if (oldLeaderPC?.CurrentItem != null) {
                        Runner.Despawn(oldLeaderPC.CurrentItem);
                        oldLeaderPC.CurrentItem = null;
                    }
                }
            }

            // 2) New leader
            CurrentLeader = newLeader;

            if (newLeader != PlayerRef.None && Runner.TryGetPlayerObject(newLeader, out var newLeaderObj)) {
                var newLeaderPC = newLeaderObj.GetComponent<PlayerController>();
                newLeaderPC?.RPC_SetLeader(true);

                // 3) Apply temporary blindness for 3 seconds
                ApplyTemporaryBlindness(newLeader);

                // 4) Spawn the LaserItem in the leader's hand
                SpawnLaserItemInHand(newLeader);
            }
        }
        public void SetLeaderMark(PlayerRef newLeader) {
            if (!Object.HasStateAuthority) {
                return;
            }

            // 1) Remove previous leader
            if (CurrentLeader != PlayerRef.None && CurrentLeader != newLeader) {
                if (Runner.TryGetPlayerObject(CurrentLeader, out var oldLeaderObj)) {
                    var oldLeaderPC = oldLeaderObj.GetComponent<PlayerController>();
                    oldLeaderPC?.RPC_SetLeader(false);

                    // Remove LaserItem from old leader
                    if (oldLeaderPC?.CurrentItem != null) {
                        Runner.Despawn(oldLeaderPC.CurrentItem);
                        oldLeaderPC.CurrentItem = null;
                    }
                }
            }
            // 2) New leader
            CurrentLeader = newLeader;

            if (Runner.TryGetPlayerObject(newLeader, out var playerObj)) {
                var playerController = playerObj.GetComponent<PlayerController>();
                if (playerController != null) {
                    playerController.RPC_SetLeader(true);
                }
            }
        }

        private void SpawnLaserItemInHand(PlayerRef playerRef) {
            if (CurrentLeader == PlayerRef.None) { return; }
            if (playerRef.IsNone) { return; }

            var pd = GameManager.Instance.PlayerData[playerRef];

            if (!pd.IsAlive) {
                return;
            }

            if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                PlayerController pc = playerObj.GetComponent<PlayerController>();
                if (pc == null) {
                    return;
                }
                Transform spawnHolder = pc.thirdPersonItemHolder;
                Vector3 spawnPos = (spawnHolder != null) ? spawnHolder.position : playerObj.transform.position;
                Quaternion spawnRot = (spawnHolder != null) ? spawnHolder.rotation : Quaternion.identity;

                NetworkObject newItem = Runner.Spawn(laserItem, spawnPos, spawnRot, playerRef);
                if (newItem == null) {
                    return;
                }
                if (spawnHolder != null) {
                    newItem.transform.SetParent(spawnHolder, false);
                }
                pc.CurrentItem = newItem;
            }
        }
        #endregion

        #region Player Kill Handling
        // Called when a player kills another player with LaserItem
        public void OnPlayerKilled(PlayerRef killer, PlayerRef victim) {
            if (!Object.HasStateAuthority) { return; }
            if (killer == victim) { return; }

            // Award money to killer
            if (gameManager.PlayerData.TryGet(killer, out var killerData)) {
                killerData.Money += moneyPerKill;
                gameManager.PlayerData.Set(killer, killerData);
            }

            // Kill the victim
            if (gameManager.PlayerData.TryGet(victim, out var victimData)) {
                // Only set IsAlive = false if squid game mode is enabled
                if (gameManager.IsSquidGameMode) {
                    victimData.IsAlive = false;
                }
                gameManager.PlayerData.Set(victim, victimData);
            }

            // Check if only one player remains alive
            CheckForSingleSurvivor();
        }

        private void CheckForSingleSurvivor() {
            if (!Object.HasStateAuthority) { return; }

            int aliveCount = 0;
            foreach (var kv in gameManager.PlayerData) {
                if (kv.Value.IsAlive && kv.Value.IsConnected) {
                    aliveCount++;
                }
            }

            // If only one player remains alive, end the match immediately
            if (aliveCount <= 1) {
                gameManager.EndMatch();
            }
        }
        #endregion


        #region Blindness Effects
        private void ApplyTemporaryBlindness(PlayerRef playerRef) {
            if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
                var playerController = playerObj.GetComponent<PlayerController>();
                if (playerController != null) {
                    // Apply blindness
                    playerController.RPC_SetBlindState(true);

                    // Add blindfold visual
                    AddBlindfold(playerRef);

                    // Use DOTween instead of coroutine to remove blindness after duration
                    DOVirtual.DelayedCall(blindnessDuration, () => {
                        if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
                            var playerController = playerObj.GetComponent<PlayerController>();
                            if (playerController != null) {
                                // Remove blindness
                                playerController.RPC_SetBlindState(false);

                                // Remove blindfold visual
                                RemoveBlindfold(playerRef);
                            }
                        }
                    });
                }
            }
        }



        private void AddBlindfold(PlayerRef playerRef) {
            if (Object.HasStateAuthority) {
                RPC_AddBlindfold(playerRef);
            }
        }

        private void RemoveBlindfold(PlayerRef playerRef) {
            if (Object.HasStateAuthority) {
                RPC_RemoveBlindfold(playerRef);
            }
        }

        private void RemoveAllBlindfolds() {
            if (Object.HasStateAuthority) {
                RPC_RemoveAllBlindfolds();
            }
        }

        private Transform FindHeadBone(Transform root) {
            // Try to find head bone in common locations
            Transform[] bones = root.GetComponentsInChildren<Transform>();
            foreach (Transform bone in bones) {
                if (bone.name.ToLower().Contains("head") ||
                    bone.name.ToLower().Contains("skull") ||
                    bone.name.ToLower().Contains("cranium")) {
                    return bone;
                }
            }
            return null;
        }
        #endregion

        #region RPCs
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_RemoveBlindfold(PlayerRef playerRef) {
            if (playerBlindfolds.TryGetValue(playerRef, out GameObject blindfold)) {
                if (blindfold != null) {
                    Destroy(blindfold);
                }
                playerBlindfolds.Remove(playerRef);
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_AddBlindfold(PlayerRef playerRef) {
            if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
                PlayerController player = playerObj.GetComponent<PlayerController>();
                if (player != null && blindfoldPrefab != null && !playerBlindfolds.ContainsKey(playerRef)) {
                    // Find the head bone in the player's skeleton
                    Transform headBone = FindHeadBone(player.thirdPersonRoot.transform);

                    if (headBone != null) {
                        // Use exact original rotation from prefab with offset
                        Vector3 localOffset = new Vector3(0, 0.3f, -0.01f);
                        Vector3 blindfoldPosition = headBone.position + localOffset;
                        GameObject blindfold = Instantiate(blindfoldPrefab, blindfoldPosition, blindfoldPrefab.transform.rotation, headBone);
                        playerBlindfolds[playerRef] = blindfold;
                    }
                    else {
                        // Fallback: use world position if head bone not found
                        Vector3 headPosition = player.cameraHandle.position;
                        Vector3 blindfoldOffset = new Vector3(0, 0.3f, -0.01f);
                        Vector3 blindfoldPosition = headPosition + blindfoldOffset;
                        Quaternion blindfoldRotation = blindfoldPrefab.transform.rotation;

                        GameObject blindfold = Instantiate(blindfoldPrefab, blindfoldPosition, blindfoldRotation, player.thirdPersonRoot.transform);
                        playerBlindfolds[playerRef] = blindfold;
                    }
                }
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_RemoveAllBlindfolds() {
            foreach (var kv in playerBlindfolds) {
                if (kv.Value != null) {
                    Destroy(kv.Value);
                }
            }
            playerBlindfolds.Clear();
        }
        #endregion
    }
}
