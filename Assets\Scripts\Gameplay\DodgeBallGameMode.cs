using UnityEngine;
using Fusion;
using SimpleFPS;
using System.Collections.Generic;

namespace SimpleFPS {
    /// <summary>
    /// DodgeBall Game Mode - Team-based game where players throw footballs at each other.
    /// Blue vs Red teams. Score points for hitting enemy team, lose points for friendly fire.
    /// Killed players respawn but maintain isAlive status during match.
    /// Losing team (lower total score) gets isAlive = false at the end.
    /// </summary>
    public class DodgeBallGameMode : NetworkBehaviour, IGameModeConfig {
        public static DodgeBallGameMode Instance { get; private set; }

        #region Fields
        [Header("Gameplay Settings")]
        [SerializeField] private float lobbyDuration = 30f;
        [SerializeField] private float matchDuration = 180f; // 3 minutes for dodgeball
        [SerializeField] private float playerRespawnTime = 3f;
        [SerializeField] private GameManager gameManager;

        [Header("Mode Description")]
        [TextArea]
        [SerializeField] private string modeDescription = "DodgeBall: Blue vs Red teams. Hit enemies (+100 coins), avoid friendly fire (-200 coins). Losing team dies at the end!";

        [Header("References")]
        [SerializeField] private Transform loserSpawnPoint;
        [SerializeField] private NetworkPrefabRef footballPrefab;
        [SerializeField] private int footballCount = 5; // Number of footballs to spawn

        [Header("Team Materials")]
        [SerializeField] private Material blueMaterial;
        [SerializeField] private Material redMaterial;
        [SerializeField] private Material defaultMaterial; // Default red material

        [Header("Scoring")]
        [SerializeField] private int enemyKillReward = 100;
        [SerializeField] private int friendlyFirePenalty = 200;
        #endregion

        #region Properties
        public float LobbyDuration => lobbyDuration;
        public float MatchDuration => matchDuration;
        public float PlayerRespawnTime => playerRespawnTime;
        public Transform LoserSpawnPoint => loserSpawnPoint;
        public string ModeDescription => modeDescription;
        #endregion

        #region Networked Properties
        [Networked] public int BlueTeamScore { get; set; }
        [Networked] public int RedTeamScore { get; set; }
        #endregion

        #region Private Fields
        private Dictionary<PlayerRef, int> playerTeamAssignments = new Dictionary<PlayerRef, int>();
        #endregion

        #region Unity Lifecycle
        private void Awake() {
            if (Instance == null) {
                Instance = this;
            }
            else {
                Destroy(gameObject);
            }
            if (gameManager == null) {
                gameManager = GameManager.Instance;
            }
        }

        public override void Spawned() {
            if (!Object.HasStateAuthority) {
                enabled = false;
                return;
            }
        }
        #endregion

        #region Round Logic
        public void OnRoundStarted() {
            if (!Object.HasStateAuthority) return;

            // Reset scores
            BlueTeamScore = 0;
            RedTeamScore = 0;

            // Assign teams and set materials
            AssignPlayersToTeams();

            // Spawn footballs
            SpawnFootballs();
        }

        public void OnRoundEnded() {
            if (!Object.HasStateAuthority) return;
            DetermineWinnersAndLosers();
        }

        public void DetermineWinnersAndLosers() {
            if (!Object.HasStateAuthority) return;

            // Determine winning team based on score
            bool blueWins = BlueTeamScore > RedTeamScore;
            bool isDraw = BlueTeamScore == RedTeamScore;

            // Set isAlive status based on team performance
            foreach (var kv in gameManager.PlayerData) {
                var playerRef = kv.Key;
                var playerData = kv.Value;

                if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                    var pc = playerObj.GetComponent<PlayerController>();
                    if (pc != null) {
                        // Determine if player survives
                        if (isDraw) {
                            // Draw - everyone loses (only if squid game mode is enabled)
                            if (gameManager.IsSquidGameMode) {
                                playerData.IsAlive = false;
                            }
                        }
                        else if ((pc.TeamId == 1 && blueWins) || (pc.TeamId == 2 && !blueWins)) {
                            // Winning team survives
                            playerData.IsAlive = true;
                        }
                        else {
                            // Losing team dies (only if squid game mode is enabled)
                            if (gameManager.IsSquidGameMode) {
                                playerData.IsAlive = false;
                            }
                        }

                        // Reset team and material
                        pc.TeamId = 0;
                        pc.RPC_SetTeamMaterial(0);
                        gameManager.PlayerData.Set(playerRef, playerData);
                    }
                }
            }
        }

        public void ResetModeState() {
            if (!Object.HasStateAuthority) return;

            BlueTeamScore = 0;
            RedTeamScore = 0;

            // Clear team assignments
            playerTeamAssignments.Clear();

            // Reset all player teams and materials
            foreach (var kv in gameManager.PlayerData) {
                var playerRef = kv.Key;
                if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                    var pc = playerObj.GetComponent<PlayerController>();
                    if (pc != null) {
                        pc.TeamId = 0;
                        pc.RPC_SetTeamMaterial(0);
                    }
                }
            }
        }
        #endregion

        #region Material Management
        public Material GetMaterialForTeam(int teamId) {
            return teamId switch {
                1 => blueMaterial,
                2 => redMaterial,
                _ => defaultMaterial
            };
        }
        #endregion

        #region Player Spawn Management
        public void RestorePlayerTeamOnSpawn(PlayerController player) {
            if (!Object.HasStateAuthority) return;

            // Find the player's team assignment from stored data
            var playerRef = player.Object.InputAuthority;
            if (gameManager.PlayerData.TryGet(playerRef, out var playerData)) {
                // Restore team assignment based on stored team data
                // We'll store team info in a separate dictionary since PlayerData doesn't have team field
                if (playerTeamAssignments.TryGetValue(playerRef, out int teamId)) {
                    player.TeamId = teamId;
                    player.RPC_SetTeamMaterial(teamId);
                }
            }
        }
        #endregion

        #region Team Management
        private void AssignPlayersToTeams() {
            if (!Object.HasStateAuthority) {
                return;
            }

            List<PlayerRef> connectedPlayers = new List<PlayerRef>();

            // Get all connected and alive players
            foreach (var kv in gameManager.PlayerData) {
                if (kv.Value.IsConnected && kv.Value.IsAlive) {
                    connectedPlayers.Add(kv.Key);
                }
            }

            // Sort players by their ID to ensure consistent ordering
            connectedPlayers.Sort((a, b) => a.RawEncoded.CompareTo(b.RawEncoded));

            // Shuffle the list to randomize team assignments
            for (int i = 0; i < connectedPlayers.Count; i++) {
                int randomIndex = UnityEngine.Random.Range(i, connectedPlayers.Count);
                var temp = connectedPlayers[i];
                connectedPlayers[i] = connectedPlayers[randomIndex];
                connectedPlayers[randomIndex] = temp;
            }

            // Clear previous assignments
            playerTeamAssignments.Clear();

            // If only one player, assign to blue team
            if (connectedPlayers.Count == 1) {
                var playerRef = connectedPlayers[0];
                if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                    var pc = playerObj.GetComponent<PlayerController>();
                    if (pc != null) {
                        pc.TeamId = 1; // Blue team
                        pc.RPC_SetTeamMaterial(1);
                        playerTeamAssignments[playerRef] = 1; // Store assignment
                    }
                }
                return;
            }

            // Assign teams alternately
            for (int i = 0; i < connectedPlayers.Count; i++) {
                var playerRef = connectedPlayers[i];
                if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                    var pc = playerObj.GetComponent<PlayerController>();
                    if (pc != null) {
                        int teamId = (i % 2) + 1; // 1 for blue, 2 for red

                        pc.TeamId = teamId;
                        pc.RPC_SetTeamMaterial(teamId);
                        playerTeamAssignments[playerRef] = teamId; // Store assignment
                    }
                }
            }
        }
        #endregion

        #region Football Management
        private void SpawnFootballs() {
            if (!Object.HasStateAuthority) return;

            for (int i = 0; i < footballCount; i++) {
                Vector3 spawnPos = GetRandomSpawnPosition();
                Runner.Spawn(footballPrefab, spawnPos, Quaternion.identity);
            }
        }

        private Vector3 GetRandomSpawnPosition() {
            // Get random spawn point from SpawnManager
            if (SpawnManager.Instance != null) {
                Transform spawnPoint = SpawnManager.Instance.GetRandomSpawnPoint();
                if (spawnPoint != null) {
                    // Offset slightly to avoid spawning inside players
                    return spawnPoint.position + Vector3.up * 1f + Random.insideUnitSphere * 2f;
                }
            }

            // Fallback to random position
            return new Vector3(Random.Range(-10f, 10f), 2f, Random.Range(-10f, 10f));
        }
        #endregion

        #region Scoring System
        public void OnPlayerHitByBall(PlayerRef throwerRef, PlayerRef victimRef) {
            if (!Object.HasStateAuthority) {
                return;
            }

            if (!Runner.TryGetPlayerObject(throwerRef, out NetworkObject throwerObj) ||
                !Runner.TryGetPlayerObject(victimRef, out NetworkObject victimObj)) {
                return;
            }

            var thrower = throwerObj.GetComponent<PlayerController>();
            var victim = victimObj.GetComponent<PlayerController>();

            if (thrower == null || victim == null) {
                return;
            }

            // Get player data
            if (!gameManager.PlayerData.TryGet(throwerRef, out var throwerData)) {
                return;
            }

            // Check team relationship
            bool isFriendlyFire = thrower.TeamId == victim.TeamId;
            bool isSuicide = throwerRef == victimRef;

            if (isFriendlyFire || isSuicide) {
                // Friendly fire or suicide - penalty
                throwerData.Money = Mathf.Max(0, throwerData.Money - friendlyFirePenalty);

                // Subtract from team score
                if (thrower.TeamId == 1) {
                    BlueTeamScore = Mathf.Max(0, BlueTeamScore - friendlyFirePenalty);
                }
                else if (thrower.TeamId == 2) {
                    RedTeamScore = Mathf.Max(0, RedTeamScore - friendlyFirePenalty);
                }
            }
            else {
                // Enemy hit - reward
                throwerData.Money += enemyKillReward;

                // Add to team score
                if (thrower.TeamId == 1) {
                    BlueTeamScore += enemyKillReward;
                }
                else if (thrower.TeamId == 2) {
                    RedTeamScore += enemyKillReward;
                }
            }

            gameManager.PlayerData.Set(throwerRef, throwerData);
        }
        #endregion
    }
}
