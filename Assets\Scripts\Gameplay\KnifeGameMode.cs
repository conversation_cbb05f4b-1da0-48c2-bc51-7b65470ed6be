using UnityEngine;
using Fusion;
using SimpleFPS;
using System;
using System.Collections.Generic;
using DG.Tweening;

/// <summary>
/// Knife game mode, handles spawning knives, collectible spawner, etc.
/// </summary>
public class KnifeGameMode : NetworkBehaviour, IGameModeConfig {
    public static KnifeGameMode Instance { get; private set; }


    #region Fields
    [Header("Gameplay Settings")]
    [SerializeField] private float lobbyDuration = 30f;
    [SerializeField] private float matchDuration = 60f;
    [SerializeField] private float playerRespawnTime = 5f;
    [SerializeField] private float knifeRespawnDelay = 1f;


    [Header("Mode Description")]
    [TextArea]
    [SerializeField] private string modeDescription = "Введите описание режима Knife здесь";

    [Header("References")]
    public NetworkPrefabRef knifeInHandTPO;
    [SerializeField] private GameManager gameManager;
    [SerializeField] private Transform loserSpawnPoint;

    [Header("Team Materials")]
    [SerializeField] private Material blueMaterial;
    [SerializeField] private Material redMaterial;
    [SerializeField] private Material defaultMaterial;

    [Header("Scoring")]
    [SerializeField] private int enemyKillReward = 100;
    [SerializeField] private int friendlyFirePenalty = 200;

    #endregion

    #region Networked Properties
    [Networked] public int BlueTeamScore { get; set; }
    [Networked] public int RedTeamScore { get; set; }
    #endregion

    #region Private Fields
    private Dictionary<PlayerRef, int> playerTeamAssignments = new Dictionary<PlayerRef, int>();
    private Dictionary<PlayerRef, int> playerStartingMoney = new Dictionary<PlayerRef, int>();
    #endregion


    #region Properties
    public float LobbyDuration => lobbyDuration;
    public float MatchDuration => matchDuration;
    public float PlayerRespawnTime => playerRespawnTime;
    public Transform LoserSpawnPoint => loserSpawnPoint;
    public string ModeDescription => modeDescription;

    #endregion


    #region Unity Lifecycle
    private void Awake() {
        if (Instance == null) {
            Instance = this;
        }
        else {
            Destroy(gameObject);
        }
        if (gameManager == null) {
            gameManager = GameManager.Instance;
        }
    }

    public override void Spawned() {
        if (!Object.HasStateAuthority) {
            enabled = false;
            return;
        }
    }
    #endregion


    #region Round Logic
    public void OnRoundStarted() {
        if (!Object.HasStateAuthority) {
            return;
        }

        // Reset scores
        BlueTeamScore = 0;
        RedTeamScore = 0;

        // Save starting money for each player to track earnings in this match
        playerStartingMoney.Clear();
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Value.IsAlive) {
                playerStartingMoney[kv.Key] = kv.Value.Money;
            }
        }

        // Don't reset IsAlive status - keep players who died in previous rounds as dead
        // Only assign teams and spawn items for alive players

        // Assign teams and set materials
        AssignPlayersToTeams();

        // Spawn knives for red team players
        SpawnKnivesForRedTeam();

        // Check if we have players from both teams to start the round
        CheckForEarlyRoundEnd();
    }

    public void OnRoundEnded() {
        if (!Object.HasStateAuthority) {
            return;
        }
        DetermineWinnersAndLosers();
    }
    #endregion


    #region Winners & Losers Determination
    public void DetermineWinnersAndLosers() {
        if (!Object.HasStateAuthority) return;

        const int minimumGoldRequired = 100;

        // Set isAlive status based on gold collected in this match only
        foreach (var kv in gameManager.PlayerData) {
            var playerRef = kv.Key;
            var playerData = kv.Value;

            // Only process players who were alive and participating in this round
            if (!playerData.IsConnected || !playerData.IsAlive) continue;

            if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                var pc = playerObj.GetComponent<PlayerController>();
                if (pc != null) {
                    // Calculate money earned in this match only
                    int startingMoney = playerStartingMoney.ContainsKey(playerRef) ? playerStartingMoney[playerRef] : 0;
                    int moneyEarnedThisMatch = playerData.Money - startingMoney;

                    // Check if player collected enough gold in this match
                    if (moneyEarnedThisMatch >= minimumGoldRequired) {
                        // Player collected enough gold in this match - survives
                        playerData.IsAlive = true;
                    }
                    else {
                        // Player didn't collect enough gold in this match - dies (only if squid game mode is enabled)
                        if (gameManager.IsSquidGameMode) {
                            playerData.IsAlive = false;
                        }
                    }

                    // Reset team and material
                    pc.TeamId = 0;
                    pc.RPC_SetTeamMaterial(0);
                    gameManager.PlayerData.Set(playerRef, playerData);
                }
            }
        }
    }
    #endregion


    #region Reset Mode State
    public void ResetModeState() {
        if (!Object.HasStateAuthority) {
            return;
        }

        // Reset scores
        BlueTeamScore = 0;
        RedTeamScore = 0;

        // Don't reset IsAlive status - preserve player elimination state between rounds

        // Clear team assignments and starting money tracking
        playerTeamAssignments.Clear();
        playerStartingMoney.Clear();

        // Reset all player teams and materials
        foreach (var kv in gameManager.PlayerData) {
            var playerRef = kv.Key;
            if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                var pc = playerObj.GetComponent<PlayerController>();
                if (pc != null) {
                    pc.TeamId = 0;
                    pc.RPC_SetTeamMaterial(0);
                }
            }
        }

        // Remove all knives from the scene (Despawn)
        var allKnives = FindObjectsOfType<ThrowableKnifeItem>();
        foreach (var knife in allKnives) {
            if (knife.Object != null && knife.Object.IsValid) {
                Runner.Despawn(knife.Object);
            }
        }
    }
    #endregion

    #region Team Management
    private void AssignPlayersToTeams() {
        if (!Object.HasStateAuthority) {
            return;
        }

        List<PlayerRef> connectedPlayers = new List<PlayerRef>();

        // Get all connected players
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Value.IsAlive) {
                connectedPlayers.Add(kv.Key);
            }
        }

        // Clear previous assignments
        playerTeamAssignments.Clear();

        // If only one player, assign to blue team
        if (connectedPlayers.Count == 1) {
            var playerRef = connectedPlayers[0];
            if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                var pc = playerObj.GetComponent<PlayerController>();
                if (pc != null) {
                    pc.TeamId = 1; // Blue team
                    pc.RPC_SetTeamMaterial(1);
                    playerTeamAssignments[playerRef] = 1; // Store assignment
                }
            }
            return;
        }

        // Shuffle players for random distribution
        for (int i = 0; i < connectedPlayers.Count; i++) {
            int randomIndex = UnityEngine.Random.Range(i, connectedPlayers.Count);
            var temp = connectedPlayers[i];
            connectedPlayers[i] = connectedPlayers[randomIndex];
            connectedPlayers[randomIndex] = temp;
        }

        // Assign teams alternately for balanced distribution
        for (int i = 0; i < connectedPlayers.Count; i++) {
            var playerRef = connectedPlayers[i];
            if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
                var pc = playerObj.GetComponent<PlayerController>();
                if (pc != null) {
                    int teamId = (i % 2) + 1; // 1 for blue, 2 for red

                    pc.TeamId = teamId;
                    pc.RPC_SetTeamMaterial(teamId);
                    playerTeamAssignments[playerRef] = teamId;
                }
            }
        }
    }

    private void SpawnKnivesForRedTeam() {
        if (!Object.HasStateAuthority) return;

        foreach (var kv in playerTeamAssignments) {
            PlayerRef playerRef = kv.Key;
            int teamId = kv.Value;

            // Only spawn knives for red team (teamId = 2)
            if (teamId == 2) {
                SpawnKnifeInHand(playerRef);
            }
        }
    }

    public Material GetMaterialForTeam(int teamId) {
        return teamId switch {
            1 => blueMaterial,
            2 => redMaterial,
            _ => defaultMaterial
        };
    }

    public void RestorePlayerTeamOnSpawn(PlayerController player) {
        if (!Object.HasStateAuthority) return;

        // Find the player's team assignment from stored data
        var playerRef = player.Object.InputAuthority;
        if (playerTeamAssignments.TryGetValue(playerRef, out int teamId)) {
            player.TeamId = teamId;
            player.RPC_SetTeamMaterial(teamId);
        }
    }
    #endregion


    #region Specific Game Mode Rules

    public void ScheduleKnifeRespawnForPlayer(PlayerRef player, float delay, NetworkObject thrownKnife) {
        if (!Object.HasStateAuthority) {
            return;
        }

        // Only respawn knives for red team players (TeamId = 2)
        if (Runner.TryGetPlayerObject(player, out NetworkObject playerObj)) {
            PlayerController pc = playerObj.GetComponent<PlayerController>();
            if (pc != null && pc.TeamId == 2) {

                // Use DOTween instead of coroutine for knife respawn delay
                DOVirtual.DelayedCall(delay, () => {
                    if (Runner.TryGetPlayerObject(player, out NetworkObject playerObjDelayed)) {
                        PlayerController pcDelayed = playerObjDelayed.GetComponent<PlayerController>();
                        if (pcDelayed != null && pcDelayed.CurrentItem == null && pcDelayed.TeamId == 2) {
                            SpawnKnifeInHand(player);
                            if (thrownKnife != null && thrownKnife.IsValid) {
                                Runner.Despawn(thrownKnife);
                            }
                        }
                    }
                });
            }
        }
    }

    public void SpawnKnifeInHand(PlayerRef playerRef) {
        if (!knifeInHandTPO.IsValid) {
            return;
        }
        if (playerRef.IsNone) {
            return;
        }

        var pd = GameManager.Instance.PlayerData[playerRef];

        if (!pd.IsAlive) {
            return;
        }

        if (Runner.TryGetPlayerObject(playerRef, out NetworkObject playerObj)) {
            PlayerController pc = playerObj.GetComponent<PlayerController>();
            if (pc == null) {
                return;
            }
            Transform spawnHolder = pc.thirdPersonItemHolder;
            Vector3 spawnPos = (spawnHolder != null) ? spawnHolder.position : playerObj.transform.position;
            Quaternion spawnRot = (spawnHolder != null) ? spawnHolder.rotation : Quaternion.Euler(0, 0, 0);

            NetworkObject newKnife = Runner.Spawn(knifeInHandTPO, spawnPos, spawnRot, playerRef);
            if (newKnife == null) {
                return;
            }
            if (spawnHolder != null) {
                newKnife.transform.SetParent(spawnHolder, false);
            }
            pc.CurrentItem = newKnife;
        }
    }
    #endregion

    #region Brick Hit Handling
    public void OnPlayerHitByBrick(PlayerRef victimRef) {
        if (!Object.HasStateAuthority) return;

        // First, mark the player as dead in PlayerData
        if (gameManager.PlayerData.TryGet(victimRef, out var victimData)) {
            // Only set IsAlive = false if squid game mode is enabled
            if (gameManager.IsSquidGameMode) {
                victimData.IsAlive = false;
            }
            gameManager.PlayerData.Set(victimRef, victimData);
        }

        // Check if round should end early due to team elimination
        CheckForEarlyRoundEnd();

        if (Runner.TryGetPlayerObject(victimRef, out var victimObj)) {
            var victim = victimObj.GetComponent<PlayerController>();
            if (victim != null && victim.TeamId == 2) { // Only red team players
                // Schedule knife respawn for red team player after respawn
                DOVirtual.DelayedCall(2f, () => { // 1f death delay + 5f respawn time
                    if (Runner.TryGetPlayerObject(victimRef, out var respawnedPlayerObj)) {
                        var respawnedPlayer = respawnedPlayerObj.GetComponent<PlayerController>();
                        if (respawnedPlayer != null && respawnedPlayer.TeamId == 2 && respawnedPlayer.CurrentItem == null) {
                            // Give knife to respawned red team player
                            SpawnKnifeInHand(victimRef);
                        }
                    }
                });
            }
        }
    }
    #endregion

    #region Early Round End Logic
    public void CheckForEarlyRoundEnd() {
        if (!Object.HasStateAuthority) return;

        int aliveBlueCount = 0;
        int aliveRedCount = 0;

        // Count alive players in each team
        foreach (var kv in gameManager.PlayerData) {
            if (kv.Value.IsConnected && kv.Value.IsAlive) {
                if (Runner.TryGetPlayerObject(kv.Key, out var playerObj)) {
                    var player = playerObj.GetComponent<PlayerController>();
                    if (player != null) {
                        if (player.TeamId == 1) { // Blue team
                            aliveBlueCount++;
                        }
                        else if (player.TeamId == 2) { // Red team
                            aliveRedCount++;
                        }
                    }
                }
            }
        }

        // Check if only one team remains alive
        if (aliveBlueCount > 0 && aliveRedCount == 0) {
            // Only blue team alive - blue wins
            EndRoundEarly("Blue team wins!");
        }
        else if (aliveRedCount > 0 && aliveBlueCount == 0) {
            // Only red team alive - red wins
            EndRoundEarly("Red team wins!");
        }
        else if (aliveBlueCount == 0 && aliveRedCount == 0) {
            // No one alive - draw
            EndRoundEarly("Draw - no survivors!");
        }
    }

    private void EndRoundEarly(string reason) {
        if (!Object.HasStateAuthority) return;

        Debug.Log($"Round ended early: {reason}");

        // End the match immediately
        if (gameManager != null) {
            gameManager.EndMatch();
        }
    }
    #endregion

    #region Scoring System
    public void OnPlayerHitByKnife(PlayerRef throwerRef, PlayerRef victimRef) {
        if (!Object.HasStateAuthority) {
            return;
        }

        if (!Runner.TryGetPlayerObject(throwerRef, out NetworkObject throwerObj) ||
            !Runner.TryGetPlayerObject(victimRef, out NetworkObject victimObj)) {
            return;
        }

        var thrower = throwerObj.GetComponent<PlayerController>();
        var victim = victimObj.GetComponent<PlayerController>();

        if (thrower == null || victim == null) {
            return;
        }

        // Get player data for both thrower and victim
        if (!gameManager.PlayerData.TryGet(throwerRef, out var throwerData) ||
            !gameManager.PlayerData.TryGet(victimRef, out var victimData)) {
            return;
        }

        // Set victim as not alive and move to loser spawn point
        // Only set IsAlive = false if squid game mode is enabled
        if (gameManager.IsSquidGameMode) {
            victimData.IsAlive = false;
        }
        gameManager.PlayerData.Set(victimRef, victimData);

        // Respawn victim at loser spawn point
        if (loserSpawnPoint != null) {
            victim.transform.position = loserSpawnPoint.position;
            victim.transform.rotation = loserSpawnPoint.rotation;
        }

        // Check team relationship
        bool isFriendlyFire = thrower.TeamId == victim.TeamId;
        bool isSuicide = throwerRef == victimRef;

        if (isFriendlyFire || isSuicide) {
            // Friendly fire or suicide - penalty
            throwerData.Money = Mathf.Max(0, throwerData.Money - friendlyFirePenalty);

            // Subtract from team score
            if (thrower.TeamId == 1) {
                BlueTeamScore = Mathf.Max(0, BlueTeamScore - friendlyFirePenalty);
            }
            else if (thrower.TeamId == 2) {
                RedTeamScore = Mathf.Max(0, RedTeamScore - friendlyFirePenalty);
            }
        }
        else {
            // Enemy hit - reward
            throwerData.Money += enemyKillReward;

            // Add to team score
            if (thrower.TeamId == 1) {
                BlueTeamScore += enemyKillReward;
            }
            else if (thrower.TeamId == 2) {
                RedTeamScore += enemyKillReward;
            }
        }

        // Update player data
        gameManager.PlayerData.Set(throwerRef, throwerData);

        // Check if round should end early due to team elimination
        CheckForEarlyRoundEnd();
    }
    #endregion
}
