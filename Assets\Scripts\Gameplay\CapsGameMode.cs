using Fusion;
using UnityEngine;
using SimpleFPS;
using System.Collections.Generic;
using System.Collections;

namespace SimpleFPS {
    /// <summary>
    /// Caps Game Mode - Players throw caps (chips). If the result is tails, the thrower dies.
    /// Game continues until timer runs out or only one player remains alive.
    /// If timer ends, all remaining alive players die.
    /// </summary>
    public class CapsGameMode : NetworkBehaviour, IGameModeConfig {
        public static CapsGameMode Instance { get; private set; }

        #region Fields
        [Header("Gameplay Settings")]
        [SerializeField] private float lobbyDuration = 30f;
        [SerializeField] private float matchDuration = 180f; // 3 minutes for caps game
        [SerializeField] private float playerRespawnTime = 5f;
        [SerializeField] private GameManager gameManager;

        [Header("Mode Description")]
        [TextArea]
        [SerializeField] private string modeDescription = "Caps Game: Throw caps and hope for heads! If you get tails, you die. Last player standing wins!";

        [Header("References")]
        [SerializeField] private Transform loserSpawnPoint;
        [SerializeField] private NetworkPrefabRef capsItemPrefab; // The caps item prefab to spawn
        [SerializeField] private Transform[] capsSpawnPoints; // Where to spawn caps items

        // Private fields
        private Dictionary<NetworkObject, PlayerRef> thrownCaps = new Dictionary<NetworkObject, PlayerRef>();  // Track thrown caps and their throwers       
        private bool matchAlreadyEnded = false;  // Flag to prevent multiple EndMatch calls
        #endregion

        #region Properties
        public float LobbyDuration => lobbyDuration;
        public float MatchDuration => matchDuration;
        public float PlayerRespawnTime => playerRespawnTime;
        public Transform LoserSpawnPoint => loserSpawnPoint;
        public string ModeDescription => modeDescription;
        #endregion

        #region Unity Lifecycle
        private void Awake() {
            if (Instance == null) {
                Instance = this;
            }
            else if (Instance != this) {
                Destroy(gameObject);
            }
            if (gameManager == null) {
                gameManager = GameManager.Instance;
            }
        }

        public override void Spawned() {
            if (!Object.HasStateAuthority) {
                enabled = false;
            }
            if (Instance == null) {
                Instance = this;
            }
        }
        #endregion

        #region Round Logic
        public void OnRoundStarted() {
            if (!Object.HasStateAuthority) {
                return;
            }

            // Spawn caps items at designated spawn points
            SpawnCapsItems();
        }

        public void OnRoundEnded() {
            if (!Object.HasStateAuthority) return;
            DetermineWinnersAndLosers();
        }

        public override void FixedUpdateNetwork() {
            if (!Object.HasStateAuthority) {
                return;
            }

            // Check for settled caps and handle their results
            CheckSettledCaps();
        }

        private void Update() {
            if (!Object.HasStateAuthority) return;

            // Check if only one player remains alive
            CheckForSingleSurvivor();
        }
        #endregion

        #region Winners & Losers Determination
        public void DetermineWinnersAndLosers() {
            if (!Object.HasStateAuthority) return;

            // When timer ends, check how many players are still alive
            List<PlayerRef> alivePlayers = new List<PlayerRef>();
            foreach (var kv in gameManager.PlayerData) {
                if (kv.Value.IsAlive) {
                    alivePlayers.Add(kv.Key);
                }
            }

            // If 2 or more players remain when timer ends - everyone loses
            if (alivePlayers.Count >= 2) {
                foreach (var playerRef in alivePlayers) {
                    KillPlayer(playerRef);
                }
            }
            // If only 1 player remains, they are already the winner (handled in CheckForSingleSurvivor)
            // If 0 players remain, everyone already lost
        }
        #endregion

        #region Reset Mode State
        public void ResetModeState() {
            if (!Object.HasStateAuthority) return;

            // Clear thrown caps tracking
            thrownCaps.Clear();

            // Remove all caps items from players and scene
            RemoveAllCapsItems();

            // Reset match ended flag
            matchAlreadyEnded = false;
        }
        #endregion

        #region Caps Management
        private void SpawnCapsItems() {
            if (!capsItemPrefab.IsValid || capsSpawnPoints == null) return;

            foreach (var spawnPoint in capsSpawnPoints) {
                if (spawnPoint != null) {
                    Runner.Spawn(capsItemPrefab, spawnPoint.position, spawnPoint.rotation);
                }
            }
        }

        private void SpawnNewCapsItem() {
            if (!capsItemPrefab.IsValid || capsSpawnPoints == null || capsSpawnPoints.Length == 0) return;

            // Spawn a new caps item at a random spawn point
            var randomSpawnPoint = capsSpawnPoints[Random.Range(0, capsSpawnPoints.Length)];
            if (randomSpawnPoint != null) {
                Runner.Spawn(capsItemPrefab, randomSpawnPoint.position, randomSpawnPoint.rotation);
            }
        }

        private void RemoveAllCapsItems() {
            // Remove caps from all players
            foreach (var kv in gameManager.PlayerData) {
                if (Runner.TryGetPlayerObject(kv.Key, out var playerObj)) {
                    var player = playerObj.GetComponent<PlayerController>();
                    if (player != null && player.CurrentItem != null) {
                        var capsItem = player.CurrentItem.GetComponent<ThrowableChipItem>();
                        if (capsItem != null) {
                            Runner.Despawn(player.CurrentItem);
                            player.CurrentItem = null;
                        }
                    }
                }
            }
        }
        #endregion

        #region Caps Result Handling
        /// <summary>
        /// Called when a caps item is thrown. Registers the thrower for result tracking.
        /// </summary>
        public void OnCapsThrown(NetworkObject capsObject, PlayerRef thrower) {
            if (!Object.HasStateAuthority) return;

            thrownCaps[capsObject] = thrower;
        }

        private void CheckSettledCaps() {
            List<NetworkObject> settledCaps = new List<NetworkObject>();

            foreach (var kv in thrownCaps) {
                var capsObject = kv.Key;
                var thrower = kv.Value;

                if (capsObject == null || !capsObject.IsValid) {
                    settledCaps.Add(capsObject);
                    continue;
                }

                var chipBehavior = capsObject.GetComponent<ThowableChipBehavior>();
                if (chipBehavior != null && chipBehavior.ResultSide != ThowableChipBehavior.ChipSide.Unknown) {
                    // Caps has settled, check the result
                    HandleCapsResult(thrower, chipBehavior.ResultSide);

                    settledCaps.Add(capsObject);

                    // Despawn the used caps and spawn a new one at a random spawn point
                    SpawnNewCapsItem();
                    Runner.Despawn(capsObject);
                }
            }

            // Remove settled caps from tracking (but keep the object in the scene)
            foreach (var caps in settledCaps) {
                thrownCaps.Remove(caps);
            }
        }

        private void HandleCapsResult(PlayerRef thrower, ThowableChipBehavior.ChipSide result) {
            if (result == ThowableChipBehavior.ChipSide.Tails) {
                // Player got tails - they die
                KillPlayer(thrower);
            }
            // If heads, player survives (no action needed)
        }

        private void KillPlayer(PlayerRef playerRef) {
            if (!Runner.TryGetPlayerObject(playerRef, out var playerObj)) return;

            var player = playerObj.GetComponent<PlayerController>();
            var playerDeath = playerObj.GetComponent<PlayerDeath>();

            if (player != null && playerDeath != null) {
                // Set player as not alive
                if (gameManager.PlayerData.TryGet(playerRef, out var pd)) {
                    // Only set IsAlive = false if squid game mode is enabled
                    if (gameManager.IsSquidGameMode) {
                        pd.IsAlive = false;
                    }
                    gameManager.PlayerData.Set(playerRef, pd);
                }
                // Trigger death explosion
                playerDeath.DieFromExplosion(playerRef, 0.3f);
            }
        }

        private void CheckForSingleSurvivor() {
            if (matchAlreadyEnded) return;

            List<PlayerRef> alivePlayers = new List<PlayerRef>();
            foreach (var kv in gameManager.PlayerData) {
                if (kv.Value.IsAlive) {
                    alivePlayers.Add(kv.Key);
                }
            }

            // If only one player remains, end the match immediately (they win)
            if (alivePlayers.Count == 1) {
                matchAlreadyEnded = true;
                gameManager.EndMatch();
            }
            // If no players remain, also end the match (everyone lost)
            else if (alivePlayers.Count == 0) {
                matchAlreadyEnded = true;
                gameManager.EndMatch();
            }
        }
        #endregion
    }
}


