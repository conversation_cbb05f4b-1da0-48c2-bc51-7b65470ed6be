using System.Collections;
using System.Collections.Generic;
using Fusion;
using UnityEngine;

namespace SimpleFPS {
    public struct PlayerData : INetworkStruct {
        [Networked, Capacity(24)]
        public string Nickname { get => default; set { } }
        public PlayerRef PlayerRef;
        public int CapsAmount;
        public int Kills;
        public int Deaths;
        public int LastKillTick;
        public int StatisticPosition; 
        public bool IsAlive;
        public bool IsConnected;
        public bool IsReady;
        public int AssykAmount;
        public int Money;
    }
}





