using Fusion;
using UnityEngine;
using System.Collections.Generic;
using SimpleFPS;
using DG.Tweening;

/// <summary>
/// Main manager (Singleton) that holds information about the current leader
/// and manages all "Sifa" game logic: leader changes, excluded players, and the round item.
/// </summary>
public class SifaGameMode : NetworkBehaviour, IGameModeConfig {
    public static SifaGameMode Instance { get; private set; }


    #region Fields
    [Header("Gameplay Settings")]
    [SerializeField] private float lobbyDuration = 30f;
    [SerializeField] private float matchDuration = 60f;
    [SerializeField] private float playerRespawnTime = 5f;
    [SerializeField] private GameManager gameManager;

    [Header("Mode Description")]
    [TextArea]
    [SerializeField] private string modeDescription = "Введите описание режима Knife здесь";

    [Header("References")]
    public NetworkPrefabRef sifaInHandTPO;
    public Transform sifaSpawnPoint;
    [SerializeField] private Transform loserSpawnPoint;
    [Networked] public PlayerRef CurrentLeader { get; private set; }

    [Header("Respawn Delay")]
    [SerializeField] private float sifaRespawnDelay = 1f;
    #endregion


    #region Properties
    public float LobbyDuration => lobbyDuration;
    public float MatchDuration => matchDuration;
    public float PlayerRespawnTime => playerRespawnTime;
    public Transform LoserSpawnPoint => loserSpawnPoint;
    public string ModeDescription => modeDescription;

    #endregion


    #region Unity Lifecycle
    private void Awake() {
        // Standard singleton pattern (optional but convenient).
        if (Instance == null) {
            Instance = this;
        }
        else if (Instance != this) {
            Destroy(gameObject);
        }
        if (gameManager == null) {
            gameManager = GameManager.Instance;
        }
    }

    public override void Spawned() {
        if (!Object.HasStateAuthority) {
            // We are a client — disable logic on the client side if needed.
            enabled = false;
        }
        // Ensure the singleton instance is properly assigned.
        if (Instance == null) {
            Instance = this;
        }
    }
    #endregion


    #region Round Logic
    public void OnRoundStarted() {
        if (!Object.HasStateAuthority) return;

        // If there is no leader, choose the first available alive player
        if (CurrentLeader == PlayerRef.None) {
            var candidate = gameManager.GetWinningPlayers();
            if (candidate != PlayerRef.None) {
                SetLeader(candidate);
            }
        }
        SpawnSifaItemInHands();
    }

    public void OnRoundEnded() {
        if (!Object.HasStateAuthority) { return; }
        DetermineWinnersAndLosers();
    }
    #endregion


    #region Winners & Losers Determination
    public void DetermineWinnersAndLosers() {
        // Mark only the current leader as loser
        if (gameManager.PlayerData.TryGet(CurrentLeader, out var pd)) {
            // Only set IsAlive = false if squid game mode is enabled
            if (gameManager.IsSquidGameMode) {
                pd.IsAlive = false;
            }
            gameManager.PlayerData.Set(CurrentLeader, pd);
        }
    }
    #endregion


    #region Reset Mode State
    public void ResetModeState() {
        if (!Object.HasStateAuthority) { return; }

        // Reset leader
        SetLeader(PlayerRef.None);

        // Safely remove Sifa item from all players
        if (Runner != null && gameManager != null && gameManager.PlayerData.Count > 0) {
            foreach (var pdPair in gameManager.PlayerData) {
                var playerRef = pdPair.Key;
                var playerObject = Runner.GetPlayerObject(playerRef);
                if (playerObject != null) {
                    var player = playerObject.GetComponent<PlayerController>();
                    if (player != null && player.CurrentItem != null) {
                        Runner.Despawn(player.CurrentItem);
                        player.CurrentItem = null;
                    }
                }
            }
        }

        // Safely remove all Sifa items in scene
        var allPickables = FindObjectsOfType<ThrowablePickupItem>();
        if (allPickables != null) {
            foreach (var pickable in allPickables) {
                if (pickable != null && pickable.ItemID == 3 && pickable.Object != null && pickable.Object.IsValid) {
                    Runner.Despawn(pickable.Object);
                }
            }
        }
    }
    #endregion


    #region Specific Game Mode Rules
    public void ScheduleSifaDespawnIfNotPicked(NetworkObject sifaObj, float delay) {
        if (!Object.HasStateAuthority) { return; }

        // Use DOTween instead of coroutine for despawn delay
        DOVirtual.DelayedCall(delay, () => {
            // Check if still valid
            if (sifaObj != null && sifaObj.IsValid) {
                Runner.Despawn(sifaObj);
                SpawnSifaItemInHands();
            }
        });
    }

    // Called only on the server (StateAuthority) to set a new leader.
    // The previous leader is automatically removed as leader.
    public void SetLeader(PlayerRef newLeader) {
        if (!Object.HasStateAuthority) return;

        // Remove the previous leader if present and different from the new leader
        if (CurrentLeader != PlayerRef.None && CurrentLeader != newLeader) {
            if (Runner.TryGetPlayerObject(CurrentLeader, out var oldLeaderObj)) {
                var oldLeaderPlayer = oldLeaderObj.GetComponent<PlayerController>();
                if (oldLeaderPlayer != null) {
                    oldLeaderPlayer.RPC_SetLeader(false);
                }
            }
        }

        // Set the new leader
        CurrentLeader = newLeader;
        if (newLeader != PlayerRef.None) {
            if (Runner.TryGetPlayerObject(newLeader, out var newLeaderObj)) {
                var newLeaderPlayer = newLeaderObj.GetComponent<PlayerController>();
                if (newLeaderPlayer != null) {
                    newLeaderPlayer.RPC_SetLeader(true);
                }
            }
        }
    }
    /*private void SpawnSifaItemOnScene() {
        if (!Object.HasStateAuthority) return;

        if (pickupItemSifa.IsValid && sifaSpawnPoint != null) {
            Runner.Spawn(
                pickupItemSifa,
                sifaSpawnPoint.position,
                sifaSpawnPoint.rotation,
                null
            );
        }
    }*/
    public void SpawnSifaItemInHands() {
        // Check if we have authority
        if (!Object.HasStateAuthority) { return; }
        // Check if the prefab is assigned
        if (!sifaInHandTPO.IsValid) { return; }
        // Check if there is a leader
        if (CurrentLeader == PlayerRef.None) { return; }

        // Get the leader's player object
        if (Runner.TryGetPlayerObject(CurrentLeader, out var leaderObj)) {
            var leaderController = leaderObj.GetComponent<PlayerController>();

            // Despawn old item if needed
            if (leaderController != null && leaderController.CurrentItem == null) {
                Transform spawnHolder = leaderController.thirdPersonItemHolder;
                Vector3 spawnPos = spawnHolder ? spawnHolder.position : leaderObj.transform.position;
                Quaternion spawnRot = spawnHolder ? spawnHolder.rotation : Quaternion.Euler(0, 0, 0);

                // Spawn a new Sifa in-hand
                NetworkObject newSifa = Runner.Spawn(
                    sifaInHandTPO,
                    spawnPos,
                    spawnRot,
                    CurrentLeader
                );

                if (newSifa == null) { return; }

                if (spawnHolder != null) {
                    newSifa.transform.SetParent(spawnHolder, false);
                }

                leaderController.CurrentItem = newSifa;
            }
        }
    }

    #endregion
}
