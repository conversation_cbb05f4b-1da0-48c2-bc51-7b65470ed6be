using Fusion;
using UnityEngine;
using SimpleFPS;

[RequireComponent(typeof(Collider))]
public class KlassikiLoserTrigger : NetworkBehaviour {

    [SerializeField] private GameManager gameManager;

    private void Start() {
        if (gameManager == null) {
            gameManager = GameManager.Instance;
        }
    }

    private void OnTriggerEnter(Collider other) {
        if (Object == null || !Object.HasStateAuthority) {
            return;
        }

        var pc = other.GetComponentInParent<PlayerController>();
        if (pc == null) {
            return;
        }

        var pr = pc.Object.InputAuthority;

        if (gameManager == null || !gameManager.PlayerData.TryGet(pr, out var pd)) {
            return;
        }

        // Only set IsAlive = false if squid game mode is enabled
        if (gameManager.IsSquidGameMode) {
            pd.IsAlive = false;
        }
        gameManager.PlayerData.Set(pr, pd);

        if (KlassikiGameMode.Instance != null) {
            KlassikiGameMode.Instance.RegisterFailedPlayer(pr);
        }
    }
}
