using Fusion;
using UnityEngine;
using System.Collections.Generic;
using SimpleFPS;
using System.Collections;
using Cinemachine;

namespace SimpleFPS {
    public class ZhmurkiGameMode : NetworkBehaviour, IGameModeConfig {
        public static ZhmurkiGameMode Instance { get; private set; }

        #region Fields
        [Header("Gameplay Settings")]
        [SerializeField] private float lobbyDuration = 30f;
        [SerializeField] private float matchDuration = 120f; // 2 minutes for blind tag
        [SerializeField] private float playerRespawnTime = 5f;
        [SerializeField] private GameManager gameManager;

        [Header("Mode Description")]
        [TextArea]
        [SerializeField] private string modeDescription = "Blind Tag: One player is blinded and must tag others. When tagged, you become the new blind player!";

        [Header("References")]
        public NetworkPrefabRef meleeItemInHandTPO;
        [SerializeField] private Transform loserSpawnPoint;
        [SerializeField] private GameObject blindfoldPrefab; // Blindfold visual for leader
        [SerializeField] private CollectibleSpawner collectibleSpawner;
        [Networked] public PlayerRef CurrentLeader { get; private set; }

        // Store blindfold instances for each player
        private Dictionary<PlayerRef, GameObject> playerBlindfolds = new Dictionary<PlayerRef, GameObject>();
        #endregion

        #region Properties
        public float LobbyDuration => lobbyDuration;
        public float MatchDuration => matchDuration;
        public float PlayerRespawnTime => playerRespawnTime;
        public Transform LoserSpawnPoint => loserSpawnPoint;
        public string ModeDescription => modeDescription;
        #endregion

        #region Unity Lifecycle
        private void Awake() {
            if (Instance == null) {
                Instance = this;
            }
            else if (Instance != this) {
                Destroy(gameObject);
            }
            if (gameManager == null) {
                gameManager = GameManager.Instance;
            }
        }

        public override void Spawned() {
            if (!Object.HasStateAuthority) {
                enabled = false;
            }
            if (Instance == null) {
                Instance = this;
            }
        }
        #endregion

        #region Round Logic
        public void OnRoundStarted() {
            if (!Object.HasStateAuthority) return;
            // Start spawning collectibles

            if (collectibleSpawner != null) {
                collectibleSpawner.StartSpawning();
            }

            // Choose a random player as the initial leader
            ChooseRandomLeader();

            // Spawn melee item for the leader
            SpawnMeleeItemForLeader();
        }

        public void OnRoundEnded() {
            if (!Object.HasStateAuthority) return;
            DetermineWinnersAndLosers();
        }
        #endregion

        #region Winners & Losers Determination
        public void DetermineWinnersAndLosers() {
            // The current leader loses when time expires
            if (CurrentLeader != PlayerRef.None) {
                // Remove leader effects before eliminating them
                RemoveLeaderEffects(CurrentLeader);

                if (gameManager.PlayerData.TryGet(CurrentLeader, out var pd)) {
                    // Only set IsAlive = false if squid game mode is enabled
                    if (gameManager.IsSquidGameMode) {
                        pd.IsAlive = false;
                    }
                    gameManager.PlayerData.Set(CurrentLeader, pd);
                }

                // Clear the leader reference
                CurrentLeader = PlayerRef.None;
            }
        }
        #endregion

        #region Reset Mode State
        public void ResetModeState() {
            if (!Object.HasStateAuthority) return;

            // Remove blindfolds from all players
            RemoveAllBlindfolds();

            // Remove melee items from all players
            RemoveAllMeleeItems();

            // Reset leader
            SetLeader(PlayerRef.None);
        }
        #endregion

        #region Leader Management
        private void ChooseRandomLeader() {
            if (!Object.HasStateAuthority) {
                return;
            }

            List<PlayerRef> alivePlayers = new List<PlayerRef>();
            foreach (var kv in gameManager.PlayerData) {
                if (kv.Value.IsAlive && kv.Value.IsConnected) {
                    alivePlayers.Add(kv.Key);
                }
            }

            if (alivePlayers.Count > 0) {
                int randomIndex = Random.Range(0, alivePlayers.Count);
                SetLeader(alivePlayers[randomIndex]);
            }
        }

        public void SetLeader(PlayerRef newLeader) {
            if (!Object.HasStateAuthority) return;

            // Remove previous leader's blindfold and enable camera
            if (CurrentLeader != PlayerRef.None && CurrentLeader != newLeader) {
                RemoveLeaderEffects(CurrentLeader);
            }

            // Set new leader
            CurrentLeader = newLeader;

            if (newLeader != PlayerRef.None) {
                // Apply leader effects (blindfold, disable camera)
                ApplyLeaderEffects(newLeader);

                // Transfer melee item to new leader
                TransferMeleeItemToLeader(newLeader);
            }
        }
        #endregion

        #region Leader Effects
        private void ApplyLeaderEffects(PlayerRef leaderRef) {
            if (Runner.TryGetPlayerObject(leaderRef, out var leaderObj)) {
                var leaderController = leaderObj.GetComponent<PlayerController>();
                if (leaderController != null) {
                    // Set leader flag
                    leaderController.RPC_SetLeader(true);

                    // Disable camera for leader (blind them)
                    leaderController.RPC_SetBlindState(true);

                    // Add blindfold visual
                    AddBlindfold(leaderRef);
                }
            }
        }

        private void RemoveLeaderEffects(PlayerRef leaderRef) {
            if (Runner.TryGetPlayerObject(leaderRef, out var leaderObj)) {
                var leaderController = leaderObj.GetComponent<PlayerController>();
                if (leaderController != null) {
                    // Remove leader flag
                    leaderController.RPC_SetLeader(false);

                    // Enable camera for ex-leader
                    leaderController.RPC_SetBlindState(false);

                    // Remove blindfold visual
                    RemoveBlindfold(leaderRef);
                }
            }
        }

        private void AddBlindfold(PlayerRef playerRef) {
            // Only the server should call the RPC
            if (Object.HasStateAuthority) {
                RPC_AddBlindfold(playerRef);
            }
        }

        private Transform FindHeadBone(Transform root) {
            // Look for common head bone names in Mixamo rigs
            string[] headBoneNames = { "mixamorig:Head", "Head", "head", "mixamorig:Neck", "Neck", "neck" };

            foreach (string boneName in headBoneNames) {
                Transform headBone = FindChildRecursive(root, boneName);
                if (headBone != null) {
                    return headBone;
                }
            }

            return null;
        }

        private Transform FindChildRecursive(Transform parent, string name) {
            if (parent.name == name) {
                return parent;
            }

            for (int i = 0; i < parent.childCount; i++) {
                Transform result = FindChildRecursive(parent.GetChild(i), name);
                if (result != null) {
                    return result;
                }
            }

            return null;
        }

        private void RemoveBlindfold(PlayerRef playerRef) {
            // Only the server should call the RPC
            if (Object.HasStateAuthority) {
                RPC_RemoveBlindfold(playerRef);
            }
        }


        private void RemoveAllBlindfolds() {
            // Only the server should call the RPC
            if (Object.HasStateAuthority) {
                RPC_RemoveAllBlindfolds();
            }
        }

        #endregion

        #region Touch Detection
        public void OnLeaderHitPlayer(PlayerRef hitPlayer) {
            if (!Object.HasStateAuthority) {
                return;
            }
            if (CurrentLeader == PlayerRef.None) {
                return;
            }

            // Transfer leadership to the hit player
            SetLeader(hitPlayer);
        }
        #endregion

        #region Melee Item Management
        private void SpawnMeleeItemForLeader() {
            if (!Object.HasStateAuthority) {
                return;
            }
            if (!meleeItemInHandTPO.IsValid) {
                return;
            }
            if (CurrentLeader == PlayerRef.None) {
                return;
            }

            if (Runner.TryGetPlayerObject(CurrentLeader, out var leaderObj)) {
                var leaderController = leaderObj.GetComponent<PlayerController>();
                if (leaderController != null && leaderController.CurrentItem == null) {
                    Transform spawnHolder = leaderController.thirdPersonItemHolder;
                    Vector3 spawnPos = spawnHolder ? spawnHolder.position : leaderObj.transform.position;
                    Quaternion spawnRot = spawnHolder ? spawnHolder.rotation : Quaternion.identity;

                    NetworkObject newMeleeItem = Runner.Spawn(
                        meleeItemInHandTPO,
                        spawnPos,
                        spawnRot,
                        CurrentLeader
                    );

                    if (newMeleeItem != null) {
                        leaderController.CurrentItem = newMeleeItem;
                        leaderController.OnItemChanged();
                    }
                }
            }
        }

        private void TransferMeleeItemToLeader(PlayerRef newLeader) {
            if (!Object.HasStateAuthority) {
                return;
            }

            // Remove melee item from all players first
            RemoveAllMeleeItems();

            // Spawn new melee item for the new leader
            SpawnMeleeItemForLeader();
        }

        private void RemoveAllMeleeItems() {
            if (!Object.HasStateAuthority) return;

            foreach (var kv in gameManager.PlayerData) {
                if (Runner.TryGetPlayerObject(kv.Key, out var playerObj)) {
                    var player = playerObj.GetComponent<PlayerController>();
                    if (player != null && player.CurrentItem != null) {
                        var meleeItem = player.CurrentItem.GetComponent<RulerItem>();
                        if (meleeItem != null) {
                            Runner.Despawn(player.CurrentItem);
                            player.CurrentItem = null;
                        }
                    }
                }
            }
        }
        #endregion

        #region RPCs
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_RemoveBlindfold(PlayerRef playerRef) {
            if (playerBlindfolds.TryGetValue(playerRef, out GameObject blindfold)) {
                if (blindfold != null) {
                    Destroy(blindfold);
                }
                playerBlindfolds.Remove(playerRef);
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_AddBlindfold(PlayerRef playerRef) {
            if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
                PlayerController player = playerObj.GetComponent<PlayerController>();
                if (player != null && blindfoldPrefab != null && !playerBlindfolds.ContainsKey(playerRef)) {
                    // Find the head bone in the player's skeleton
                    Transform headBone = FindHeadBone(player.thirdPersonRoot.transform);

                    if (headBone != null) {
                        // Parent blindfold to the head bone with local offset
                        Vector3 localOffset = new Vector3(0, 0.3f, -0.01f);
                        Quaternion blindfoldRotation = blindfoldPrefab.transform.rotation;

                        GameObject blindfold = Instantiate(blindfoldPrefab, headBone);
                        blindfold.transform.localPosition = localOffset;
                        blindfold.transform.localRotation = blindfoldRotation;

                        playerBlindfolds[playerRef] = blindfold;
                    }
                    else {
                        // Fallback: use world position if head bone not found
                        Vector3 headPosition = player.cameraHandle.position;
                        Vector3 blindfoldOffset = new Vector3(0, 0.3f, -0.01f);
                        Vector3 blindfoldPosition = headPosition + blindfoldOffset;
                        Quaternion blindfoldRotation = blindfoldPrefab.transform.rotation;

                        GameObject blindfold = Instantiate(blindfoldPrefab, blindfoldPosition, blindfoldRotation, player.thirdPersonRoot.transform);
                        playerBlindfolds[playerRef] = blindfold;
                    }
                }
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_RemoveAllBlindfolds() {
            foreach (var kv in playerBlindfolds) {
                if (kv.Value != null) {
                    Destroy(kv.Value);
                }
            }
            playerBlindfolds.Clear();
        }
        #endregion
    }
}
